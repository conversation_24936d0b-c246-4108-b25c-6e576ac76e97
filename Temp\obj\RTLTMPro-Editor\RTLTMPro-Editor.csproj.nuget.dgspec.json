{"format": 1, "restore": {"D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro-Editor.csproj": {}}, "projects": {"D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro-Editor.csproj", "projectName": "RTLTMPro-Editor", "projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity\\Projects\\AnimationGenerator\\Temp\\obj\\RTLTMPro-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj": {"projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413\\RuntimeIdentifierGraph.json"}}}, "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj", "projectName": "RTLTMPro", "projectPath": "D:\\Unity\\Projects\\AnimationGenerator\\RTLTMPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Unity\\Projects\\AnimationGenerator\\Temp\\obj\\RTLTMPro\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413\\RuntimeIdentifierGraph.json"}}}}}